import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { Elements } from '@stripe/react-stripe-js';
import stripePromise from '../../utils/stripe';
import { getOrder } from '../../redux/slices/orderSlice';
import StripePaymentForm from '../../components/payment/StripePaymentForm';
import LoadingSkeleton from '../../components/common/LoadingSkeleton';
import { ErrorDisplay } from '../../components/common/ErrorBoundary';
import { toast } from 'react-toastify';
import '../../styles/CheckoutPage.css';

const CheckoutPage = () => {
  const { orderId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { user } = useSelector((state) => state.auth);
  const { order, isLoading, error } = useSelector((state) => state.order);

  const [paymentStep, setPaymentStep] = useState('loading'); // loading, payment, success, error

  useEffect(() => {
    // Check if user is logged in
    if (!user) {
      toast.error('Please log in to complete your purchase');
      navigate('/login');
      return;
    }

    // Check if user is a buyer
    if (user.role !== 'buyer') {
      toast.error('Only buyers can make purchases');
      navigate('/');
      return;
    }

    // Fetch order details
    if (orderId) {
      dispatch(getOrder(orderId))
        .unwrap()
        .then(() => {
          setPaymentStep('payment');
        })
        .catch((err) => {
          console.error('Error fetching order:', err);
          toast.error('Order not found or you do not have permission to view it');
          navigate('/buyer/dashboard');
        });
    } else {
      toast.error('No order ID provided');
      navigate('/buyer/dashboard');
    }
  }, [dispatch, orderId, user, navigate]);

  const handlePaymentSuccess = (paymentResult) => {
    toast.success('Payment completed successfully!');
    setPaymentStep('success');
    
    // Navigate to success page after a short delay
    setTimeout(() => {
      navigate(`/payment-success/${orderId}`, {
        state: { paymentResult, order }
      });
    }, 2000);
  };

  const handlePaymentError = (error) => {
    console.error('Payment error:', error);
    toast.error(error.message || 'Payment failed. Please try again.');
    setPaymentStep('error');
  };

  const handlePaymentCancel = () => {
    navigate(`/buyer/details/${order?.content?._id || order?.content}`);
  };

  const handleRetryPayment = () => {
    setPaymentStep('payment');
  };

  if (isLoading || paymentStep === 'loading') {
    return <LoadingSkeleton type="checkout" />;
  }

  if (error || !order) {
    return (
      <ErrorDisplay
        title="Order Not Found"
        message={error || "The order you're looking for doesn't exist or you don't have permission to view it."}
        onRetry={() => navigate('/buyer/dashboard')}
        retryText="Go to Dashboard"
      />
    );
  }

  // Check if order belongs to current user
  if (order.buyer._id !== user.id && order.buyer !== user.id) {
    return (
      <ErrorDisplay
        title="Access Denied"
        message="You don't have permission to view this order."
        onRetry={() => navigate('/buyer/dashboard')}
        retryText="Go to Dashboard"
      />
    );
  }

  // Check if order is already paid
  if (order.paymentStatus === 'Completed') {
    return (
      <div className="checkout-page">
        <div className="max-container">
          <div className="checkout-content">
            <div className="order-already-paid">
              <h2>Order Already Paid</h2>
              <p>This order has already been completed.</p>
              <button 
                className="btn-primary"
                onClick={() => navigate('/buyer/downloads')}
              >
                View Downloads
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="checkout-page">
      <div className="max-container">
        <div className="checkout-content">
          {/* Left Section - Payment Form */}
          <div className="checkout-left">
            <div className="checkout-form-container">
              <h1 className="checkout-title">Complete Your Purchase</h1>
              
              {paymentStep === 'payment' && (
                <Elements stripe={stripePromise}>
                  <StripePaymentForm
                    order={order}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                    onCancel={handlePaymentCancel}
                  />
                </Elements>
              )}

              {paymentStep === 'success' && (
                <div className="payment-success">
                  <div className="success-icon">✅</div>
                  <h3>Payment Successful!</h3>
                  <p>Your payment has been processed successfully. Redirecting...</p>
                </div>
              )}

              {paymentStep === 'error' && (
                <div className="payment-error">
                  <div className="error-icon">❌</div>
                  <h3>Payment Failed</h3>
                  <p>There was an issue processing your payment. Please try again.</p>
                  <button 
                    className="btn-primary retry-btn"
                    onClick={handleRetryPayment}
                  >
                    Retry Payment
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Right Section - Order Summary */}
          <div className="checkout-right">
            <div className="order-summary">
              <h2 className="order-title">Order Summary</h2>

              <div className="rightbackgrounddiv">
                {/* Item Info */}
                <div className="item-info-section">
                  <h3 className="item-info-title">Item Details</h3>

                  <div className="item-details">
                    <div className="item-image">
                      <img
                        src={order.content?.thumbnailUrl || "https://via.placeholder.com/80x80/f5f5f5/666666?text=IMG"}
                        alt={order.content?.title || "Content"}
                        className="item-thumbnail"
                      />
                    </div>

                    <div className="item-description">
                      <h4 className="item-name">
                        {order.content?.title || "Content Title"}
                      </h4>
                      <p className="item-coach">
                        By {order.content?.coachName || "Coach"}
                      </p>
                      <p className="item-type">
                        {order.content?.contentType || "Digital Content"}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Order Info */}
                <div className="order-info-section">
                  <h3 className="order-info-title">Order Information</h3>
                  <div className="order-details">
                    <div className="order-row">
                      <span>Order ID:</span>
                      <span>#{order._id?.slice(-8).toUpperCase()}</span>
                    </div>
                    <div className="order-row">
                      <span>Order Type:</span>
                      <span>{order.orderType}</span>
                    </div>
                    <div className="order-row">
                      <span>Status:</span>
                      <span className={`status ${order.status?.toLowerCase()}`}>
                        {order.status}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Pricing */}
                <div className="pricing-section">
                  <div className="price-row">
                    <span className="price-label">Subtotal</span>
                    <span className="price-value">${order.amount?.toFixed(2)}</span>
                  </div>

                  {order.platformFee > 0 && (
                    <div className="price-row">
                      <span className="price-label">Platform Fee</span>
                      <span className="price-value">${order.platformFee?.toFixed(2)}</span>
                    </div>
                  )}

                  <div className="price-row total-row">
                    <span className="price-label">Total</span>
                    <span className="price-value">${order.amount?.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
